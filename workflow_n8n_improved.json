{"nodes": [{"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.type }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals", "singleValue": true}, "id": "text-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "processed_text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.type }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals", "singleValue": true}, "id": "audio-condition"}], "combinator": "and"}, "renameOutput": true, "outputKey": "processed_audio"}]}, "options": {}}, "id": "a1f15d87-1514-4e57-8587-7cba8917d073", "name": "Switch Final (Texte ou Audio)", "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-336, 192]}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "id": "cb63da2b-c646-4df9-b814-06c6520264b7", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-96, 32], "credentials": {"openAiApi": {"id": "kBorCUM6k3nCiZZm", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.chat_id }}"}, "id": "51b962a9-89c8-4750-ab6a-59dd57edd910", "name": "Simple Memory1", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [0, 0]}, {"parameters": {"promptType": "define", "text": "={{ $json.texte }}", "options": {"systemMessage": "# Role\nTu es un agent orchestrateur expert. Ta mission est de recevoir toute demande de l'utilisateur et de la transférer au sous-agent le plus adapté pour exécution. Tu **ne réalises jamais la tâche toi-même**. Tu assures que toutes les informations nécessaires sont fournies au sous-agent et que le workflow est respecté.\n\n# Sous-agents et leurs rôles\n1. **Email Agent (GML)** → Gestion complète des emails : lecture, envoi, brouillon, suivi, vérification.\n2. **Calendar Agent** → Création et gestion de meetings, planning et invitations.\n3. **CRM Agent (Airtable)** → Récupération, mise à jour et vérification des informations clients et contacts.\n4. **Drive Agent** → Recherche et gestion des fichiers Google Drive.\n\n# Outils disponibles\n- Email : Send Email, Get Email, Create Draft\n- Calendar : Création, modification et invitation à des meetings\n- CRM : Recherche et récupération de contacts et informations clients\n- Drive : Recherche de fichiers et téléchargement depuis Google Drive\n\n# Règles strictes\n1. Analyse toujours la demande utilisateur en détail pour identifier l'intention précise.\n2. Identifie le sous-agent le plus pertinent pour exécuter la tâche.\n3. **FORMATAGE POUR TELEGRAM :** **NE JAMAIS utiliser de Markdown** (interdit : **gras**, __italique__, ## titres, ```code```). Utiliser uniquement du texte brut et simple. Pour les listes : format \"1. Item A\\n2. Item B\" (sans astérisques ni symboles). Éviter absolument les caractères *, _, ~, ` qui peuvent causer des erreurs d'API. Si tu listes des fichiers : \"Nom du fichier - ID: 123abc\" (sans formatage).\n4. Formate la demande envoyée au sous-agent en JSON clair : {\"action\": \"nom_de_l_action\", \"parameters\": { ...tous les détails nécessaires... }}\n5. Pour les actions email avec adresse explicitement fournie dans le message, transférer directement à l'Email Agent SANS vérification CRM.\n6. Pour les contacts nominaux sans adresse email, utiliser le CRM Agent pour récupérer les informations.\n7. Calendar Agent ne doit être utilisé **qu'après** avoir toutes les informations nécessaires (date, heure, participants).\n8. Si la demande est ambiguë ou ne correspond à aucun sous-agent, demande à l'utilisateur de clarifier immédiatement.\n9. Ne jamais deviner des informations. Si une information est manquante et ne peut pas être trouvée dans le CRM ou Drive Agent, demande-la explicitement à l'utilisateur.\n10. Pour les adresses email, toujours conserver les caractères spéciaux valides (_, -, .) et ne pas modifier l'adresse fournie par l'utilisateur.\n\n# Instructions spécifiques pour emails\n- Pour lecture d'emails : Derniers emails → `in:inbox newer_than:7d`, Email d'un expéditeur précis → `from:<EMAIL>`, Email avec mot-clé → `subject:\"mot clé\"`, Tous les emails non lus → `is:unread`\n- Pour l'envoi ou la création de brouillon : Vérifie que les emails des destinataires sont disponibles. Si non, récupère via CRM Agent. Inclure sujet, corps (en HTML si possible), destinataires et toute pièce jointe si nécessaire. Si le sujet est manquant, génère-le automatiquement à partir du contenu.\n\n# Processus de réponse\n1. Recevoir la demande utilisateur\n2. Identifier le sous-agent compétent\n3. Transmettre les instructions au sous-agent avec tous les paramètres nécessaires\n4. Recevoir la réponse du sous-agent\n5. **Formater la réponse finale en TEXTE BRUT pour Telegram** (aucun markdown)\n6. Renvoyer la réponse à l'utilisateur\n\n# Exemples de formatage CORRECT\n✅ \"Voici la liste des fichiers :\\n1. Rapport.pdf ; Budget.xlsx -\n✅ \"Email de Marc Diomande : <EMAIL>\"\n✅ \"Meeting créé pour le 15 janvier à 14<NAME_EMAIL>\"\n\n# Exemples de formatage INTERDIT\n❌ \"**Voici** la liste des __fichiers__ :\"\n❌ \"```FICHE IVORIENNE``` Id: 1BUZ...\"\n❌ \"## RÉSULTATS ##\\n* Item 1\\n* Item 2\""}}, "id": "da8eb590-0599-4134-84b8-ca6c5fdc9a26", "name": "Agent <PERSON><PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [-96, 208]}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-208, 720], "id": "2c1a48fd-dc65-4667-9268-afeed2606054", "name": "OpenAI Chat Model4", "credentials": {"openAiApi": {"id": "kBorCUM6k3nCiZZm", "name": "OpenAi account"}}}, {"parameters": {"text": "={{ $fromAI('Prompt__User_Message_', '', 'string') }}", "options": {"systemMessage": "## Role\nTu es un **sous-agent expert** en gestion de fichiers sur Google Drive.\nTa mission est de **recevoir les instructions de l'Agent Orchestrateur** et de les exécuter **exactement**, en utilisant les outils Google Drive à ta disposition.\nTu ne prends **aucune initiative** en dehors des instructions reçues.\n\n## 🛠 Outils disponibles\n\n1️⃣ **Search files and folders in Drive** → Rechercher des fichiers/dossiers selon des critères précis.\n2️⃣ **Download File from Drive** → Télécharger un fichier à partir de son ID.\n\n---\n\n## 📁 Actions détaillées\n\n### 1️⃣ Rechercher des fichiers (Search files and folders in Drive)\n\n* Pour lister TOUS les fichiers : utilise une recherche vide ou avec des critères très larges\n* Pour rechercher par nom : utilise le nom du fichier ou une partie\n* Pour rechercher par type : utilise l'extension (.pdf, .docx, .xlsx, etc.)\n* Retourne TOUJOURS la liste complète avec le format :\n  - Nom du fichier\n  - ID du fichier\n  - Type de fichier\n  - Date de création/modification\n* NE JAMAIS inventer ou halluciner des fichiers qui ne sont pas dans les résultats de la recherche\n* Si aucun fichier n'est trouvé, l'indiquer clairement\n\n### 2️⃣ Télécharger un fichier (Download File from Drive)\n\n* Vérifie que tu disposes de l'**ID du fichier** (`fileId`).\n* Si l'ID est manquant, **utilise d'abord l'outil de recherche** pour le trouver.\n* Télécharge le fichier exactement tel qu'il est.\n* Retourne la réponse sous forme JSON avec au minimum :\n  ```json\n  {\n    \"nom_fichier\": \"\",\n    \"id\": \"\",\n    \"type\": \"\",\n    \"date_creation\": \"\",\n    \"status\": \"téléchargé avec succès\"\n  }\n  ```\n\n---\n\n## ⚖️ Règles générales\n\n* Exécute **uniquement** ce qui est demandé par l'orchestrateur.\n* **JAMAIS d'hallucinations** : ne jamais inventer des fichiers qui n'existent pas\n* Si une information essentielle est manquante, **ne jamais deviner** et signaler.\n* Utilise TOUJOURS les outils disponibles pour obtenir les informations réelles\n* Si la recherche ne retourne aucun résultat, l'indiquer clairement\n* Formate les réponses de manière claire et structurée\n* Ne jamais inclure de **placeholder** ni de données inventées"}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [-96, 544], "id": "ea578aa0-4bac-4109-a17c-4ed36b440149", "name": "Drive Agent"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Utilise cet outil pour télécharger un fichier depuis Google Drive. Tu as besoin de l'ID du fichier.", "operation": "download", "fileId": "={{ $fromAI('File_ID', 'L\\'ID du fichier Google Drive à télécharger', 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleDriveTool", "typeVersion": 3, "position": [144, 832], "id": "501992cc-c24a-4d85-9e43-178fef97a8dd", "name": "Download File from Drive", "credentials": {"googleDriveOAuth2Api": {"id": "wOjnKyC1cbbP8pfB", "name": "Google Drive account 2"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Utilise cet outil pour rechercher des fichiers et dossiers dans Google Drive. Configuration améliorée pour récupérer TOUS les fichiers sans limitation.", "resource": "fileFolder", "filter": {"query": "trashed=false"}, "options": {"fields": ["id", "name", "mimeType", "createdTime", "modifiedTime", "size", "webViewLink"], "limit": 1000, "returnAll": true}}, "type": "n8n-nodes-base.googleDriveTool", "typeVersion": 3, "position": [0, 816], "id": "781b4d7d-59c3-43fa-b14c-408d01206375", "name": "Search files and folders in Drive", "credentials": {"googleDriveOAuth2Api": {"id": "wOjnKyC1cbbP8pfB", "name": "Google Drive account 2"}}}, {"parameters": {"chatId": "={{ $('Switch Final (Texte ou Audio)').item.json.chat_id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [304, 144], "id": "9a85a5e2-d04f-4e21-9294-95b6bb63cc4c", "name": "Send a text message1", "webhookId": "35c2f4b4-6f2d-4deb-bbe4-4d5709d1fa4f", "credentials": {"telegramApi": {"id": "3oesNskXwd51vmJp", "name": "Telegram account"}}}], "connections": {"Switch Final (Texte ou Audio)": {"main": [[{"node": "Agent <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Agent <PERSON><PERSON><PERSON>", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory1": {"ai_memory": [[{"node": "Agent <PERSON><PERSON><PERSON>", "type": "ai_memory", "index": 0}]]}, "Agent Orchestrateur": {"main": [[{"node": "Send a text message1", "type": "main", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "Drive Agent", "type": "ai_languageModel", "index": 0}]]}, "Drive Agent": {"ai_tool": [[{"node": "Agent <PERSON><PERSON><PERSON>", "type": "ai_tool", "index": 0}]]}, "Download File from Drive": {"ai_tool": [[{"node": "Drive Agent", "type": "ai_tool", "index": 0}]]}, "Search files and folders in Drive": {"ai_tool": [[{"node": "Drive Agent", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d6975eff1f9c7cf314080fdca8269f46845caf65b3c488e39be46a24bac9a52"}}